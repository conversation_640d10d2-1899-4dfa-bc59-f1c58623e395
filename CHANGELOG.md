# Changelog

## [1.6.0](https://github.com/cgoinglove/mcp-client-chatbot/compare/v1.5.2...v1.6.0) (2025-06-01)


### Features

* add husky for formatting and checking commits  ([#71](https://github.com/cgoinglove/mcp-client-chatbot/issues/71)) ([a379cd3](https://github.com/cgoinglove/mcp-client-chatbot/commit/a379cd3e869b5caab5bcaf3b03f5607021f988ef))
* add Spanish, French, Japanese, and Chinese language support with UI improvements ([#74](https://github.com/cgoinglove/mcp-client-chatbot/issues/74)) ([e34d43d](https://github.com/cgoinglove/mcp-client-chatbot/commit/e34d43df78767518f0379a434f8ffb1808b17e17))
* implement cold start-like auto connection for MCP server and simplify status ([#73](https://github.com/cgoinglove/mcp-client-chatbot/issues/73)) ([987c442](https://github.com/cgoinglove/mcp-client-chatbot/commit/987c4425504d6772e0aefe08b4e1911e4cb285c1))


## [1.5.2](https://github.com/cgoinglove/mcp-client-chatbot/compare/v1.5.1...v1.5.2) (2025-06-01)


### Features

* Add support for Streamable HTTP Transport [#56](https://github.com/cgoinglove/mcp-client-chatbot/issues/56) ([#64](https://github.com/cgoinglove/mcp-client-chatbot/issues/64)) ([8783943](https://github.com/cgoinglove/mcp-client-chatbot/commit/878394337e3b490ec2d17bcc302f38c695108d73))
* implement speech system prompt and update voice chat options for enhanced user interaction ([5a33626](https://github.com/cgoinglove/mcp-client-chatbot/commit/5a336260899ab542407c3c26925a147c1a9bba11))
* update MCP server UI and translations for improved user experience ([1e2fd31](https://github.com/cgoinglove/mcp-client-chatbot/commit/1e2fd31f8804669fbcf55a4c54ccf0194a7e797c))


### Bug Fixes

* enhance mobile UI experience with responsive design adjustments ([2eee8ba](https://github.com/cgoinglove/mcp-client-chatbot/commit/2eee8bab078207841f4d30ce7708885c7268302e))
* UI improvements for mobile experience ([#66](https://github.com/cgoinglove/mcp-client-chatbot/issues/66)) ([b4349ab](https://github.com/cgoinglove/mcp-client-chatbot/commit/b4349abf75de69f65a44735de2e0988c6d9d42d8))


### Miscellaneous Chores

* release 1.5.2 ([d185514](https://github.com/cgoinglove/mcp-client-chatbot/commit/d1855148cfa53ea99c9639f8856d0e7c58eca020))
