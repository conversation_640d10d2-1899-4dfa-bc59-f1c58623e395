import type { MCPServerConfig } from "app-types/mcp";
import { dirname } from "path";
import { mkdir, readFile, writeFile } from "fs/promises";
import type {
  MCPClientsManager,
  MCPConfigStorage,
} from "./create-mcp-clients-manager";
import chokidar from "chokidar";
import type { FSWatcher } from "chokidar";
import { createDebounce, objectFlow } from "lib/utils";
import equal from "fast-deep-equal";
import defaultLogger from "logger";
import { MCP_CONFIG_PATH } from "lib/ai/mcp/config-path";
import { colorize } from "consola/utils";

const logger = defaultLogger.withDefaults({
  message: colorize("gray", `MCP File Config Storage: `),
});

/**
 * Creates a file-based implementation of MCPServerStorage
 */
export function createFileBasedMCPConfigsStorage(
  path?: string,
): MCPConfigStorage {
  const configPath = path || MCP_CONFIG_PATH;
  let watcher: FSWatcher | null = null;
  let manager: MCPClientsManager;
  const debounce = createDebounce();

  /**
   * Reads config from file
   */
  async function readConfigFile(): Promise<Record<string, MCPServerConfig>> {
    try {
      const configText = await readFile(configPath, { encoding: "utf-8" });
      return JSON.parse(configText ?? "{}");
    } catch (err: any) {
      if (err.code === "ENOENT") {
        return {};
      } else if (err instanceof SyntaxError) {
        throw new Error(
          `Config file ${configPath} has invalid JSON: ${err.message}`,
        );
      } else {
        throw err;
      }
    }
  }

  /**
   * Writes config to file
   */
  async function writeConfigFile(
    config: Record<string, MCPServerConfig>,
  ): Promise<void> {
    const dir = dirname(configPath);
    await mkdir(dir, { recursive: true });
    await writeFile(configPath, JSON.stringify(config, null, 2), "utf-8");
  }

  async function checkAndRefreshClients() {
    try {
      logger.debug("Checking MCP clients Diff");
      const fileConfig = await readConfigFile();

      const fileConfigs = Object.entries(fileConfig)
        .map(([name, config]) => ({ name, config }))
        .sort((a, b) => a.name.localeCompare(b.name));

      // Get current manager configs
      const managerConfigs = await manager
        .getClients()
        .then((clients) =>
          clients.map((client) => {
            const info = client.getInfo();
            return {
              name: info.name,
              config: info.config,
            };
          }),
        )
        .then((configs) =>
          configs.sort((a, b) => a.name.localeCompare(b.name)),
        );

      let shouldRefresh = false;
      if (fileConfigs.length !== managerConfigs.length) {
        shouldRefresh = true;
      } else if (!equal(fileConfigs, managerConfigs)) {
        shouldRefresh = true;
      }

      if (shouldRefresh) {
        const refreshPromises = fileConfigs.map(async ({ name, config }) => {
          const managerConfig = await manager
            .getClients()
            .then((clients) => clients.find((c) => c.getInfo().name === name))
            .then((c) => c?.getInfo());
          if (!managerConfig) {
            logger.debug(`Adding MCP client ${name}`);
            return manager.addClient(name, config);
          }
          if (!equal(managerConfig.config, config)) {
            logger.debug(`Refreshing MCP client ${name}`);
            return manager.refreshClient(name, config);
          }
        });
        const deletePromises = managerConfigs
          .filter((c) => {
            const fileConfig = fileConfigs.find((c2) => c2.name === c.name);
            return !fileConfig;
          })
          .map((c) => {
            logger.debug(`Removing MCP client ${c.name}`);
            return manager.removeClient(c.name);
          });
        await Promise.allSettled([...refreshPromises, ...deletePromises]);
      }
    } catch (err) {
      logger.error("Error checking and refreshing clients:", err);
    }
  }

  /**
   * Initializes storage by reading existing config or creating empty file
   */
  async function init(_manager: MCPClientsManager): Promise<void> {
    manager = _manager;

    // Stop existing watcher if any
    if (watcher) {
      await watcher.close();
      watcher = null;
    }

    // Ensure config file exists
    try {
      await readConfigFile();
    } catch (err: any) {
      if (err.code === "ENOENT") {
        // Create empty config file if doesn't exist
        await writeConfigFile({});
      } else {
        throw err;
      }
    }

    // Setup file watcher
    watcher = chokidar.watch(configPath, {
      persistent: true,
      awaitWriteFinish: true,
      ignoreInitial: true,
    });

    watcher.on("change", () => debounce(checkAndRefreshClients, 1000));
  }

  return {
    init,
    async loadAll(): Promise<Record<string, MCPServerConfig>> {
      return await readConfigFile();
    },
    // Saves a configuration with the given name
    async save(name: string, config: MCPServerConfig): Promise<void> {
      const currentConfig = await readConfigFile();
      currentConfig[name] = config;
      await writeConfigFile(currentConfig);
    },
    // Deletes a configuration by name
    async delete(name: string): Promise<void> {
      const currentConfig = await readConfigFile();
      const newConfig = objectFlow(currentConfig).filter(
        (_, key) => key !== name,
      );
      await writeConfigFile(newConfig);
    },

    // Checks if a configuration exists
    async has(name: string): Promise<boolean> {
      const currentConfig = await readConfigFile();
      return name in currentConfig;
    },
  };
}
