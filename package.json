{"name": "mcp-client-chatbot", "version": "1.6.0", "private": true, "author": "cgoinglove", "license": "MIT", "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "build:local": "cross-env NO_HTTPS='1' next build", "test": "vitest run", "test:watch": "vitest", "lint": "next lint && biome lint --write --unsafe", "lint:fix": "next lint --fix && biome lint --write --unsafe", "format": "biome format --write", "check-types": "tsc --noEmit", "initial:env": "tsx scripts/initial-env.ts", "postinstall": "tsx scripts/postinstall.ts", "clean": "tsx scripts/clean.ts", "db:generate": "drizzle-kit generate", "db:reset": "drizzle-kit drop && drizzle-kit push", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:migrate": "tsx scripts/db-migrate.ts", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "docker-compose:up": "docker-compose -f docker/compose.yml up -d --build", "docker-compose:down": "docker-compose -f docker/compose.yml down", "docker-compose:logs": "docker-compose -f docker/compose.yml logs -f", "docker-compose:ps": "docker-compose -f docker/compose.yml ps", "docker-compose:update": "git pull && docker-compose -f docker/compose.yml up -d --build", "docker:pg": "docker run --name mcp-pg -e POSTGRES_PASSWORD=your_password -e POSTGRES_USER=your_username -e POSTGRES_DB=your_database_name -p 5432:5432 -d postgres", "docker:app": "docker build -f docker/Dockerfile -t mcp-client-chatbot . && docker run -p 3000:3000 -e NO_HTTPS=1 mcp-client-chatbot", "prepare": "husky"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.11", "@ai-sdk/google": "^1.2.18", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@ai-sdk/xai": "^1.2.16", "@modelcontextprotocol/sdk": "^1.11.5", "@openrouter/ai-sdk-provider": "^0.4.6", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@tiptap/extension-mention": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@tiptap/suggestion": "^2.12.0", "ai": "^4.3.16", "bcrypt-ts": "^7.0.0", "better-auth": "^1.2.8", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "consola": "^3.4.2", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.41.0", "fast-deep-equal": "^3.1.3", "framer-motion": "^12.12.1", "hast-util-to-jsx-runtime": "^2.3.6", "lucide-react": "^0.486.0", "mermaid": "^11.6.0", "nanoid": "^5.1.5", "next": "15.3.2", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "ollama-ai-provider": "^1.2.0", "pg": "^8.16.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.9", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "shiki": "^3.4.2", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "ts-safe": "^0.0.5", "tw-animate-css": "^1.3.0", "vaul": "^1.1.2", "zod": "^3.25.20", "zod-to-json-schema": "^3.24.5", "zustand": "^5.0.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^20.17.50", "@types/pg": "^8.15.2", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "cross-env": "^7.0.3", "drizzle-kit": "^0.30.6", "eslint": "^9.27.0", "eslint-config-next": "15.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.0", "rimraf": "^6.0.1", "tailwindcss": "^4.1.7", "tsx": "^4.19.4", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.4"}, "lint-staged": {"*.{js,json,md,mjs,ts,yaml,tsx,css}": ["pnpm format", "pnpm lint:fix"]}, "packageManager": "pnpm@10.2.1", "engines": {"node": ">=18"}, "pnpm": {"onlyBuiltDependencies": ["@biomejs/biome", "@tailwindcss/oxide", "esbuild", "sharp", "unrs-resolver"]}}