{"Common": {"cancel": "取消", "update": "更新", "continue": "继续", "delete": "删除", "save": "保存", "back": "返回", "next": "下一步", "create": "创建", "showLess": "显示更少", "showMore": "显示更多", "generate": "生成"}, "Auth": {"SignIn": {"title": "欢迎回来", "description": "登录您的账户以继续", "oauthClientIdNotSet": "{provider} 客户端 ID 未设置", "noAccount": "没有账户？", "signUp": "注册", "signIn": "登录", "orContinueWith": "或继续使用"}, "SignUp": {"title": "创建账户", "signIn": "登录", "description": "注册您的账户", "step1": "通过输入您的邮箱地址开始您的旅程", "step2": "我会在聊天时使用这个名字", "step3": "创建一个强密码来保护您的账户", "signUp": "注册", "invalidEmail": "无效的邮箱地址", "emailAlreadyExists": "邮箱已存在", "nameRequired": "姓名是必需的", "passwordRequired": "密码是必需的", "createAccount": "创建账户"}, "Intro": {"description": "欢迎来到 MCP 聊天机器人。登录以体验我们的 AI 驱动的对话工具。"}}, "Chat": {"Error": "聊天错误", "thisMessageWasNotSavedPleaseTryTheChatAgain": "此消息未保存。请重试聊天。", "Greeting": {"goodMorning": "早上好，{name}", "goodAfternoon": "下午好，{name}", "goodEvening": "晚上好，{name}", "niceToSeeYouAgain": "很高兴再次见到您，{name}", "whatAreYouWorkingOnToday": "您今天在做什么，{name}？", "letMeKnowWhenYoureReadyToBegin": "准备好开始时请告诉我。", "whatAreYourThoughtsToday": "您今天有什么想法？", "whereWouldYouLikeToStart": "您想从哪里开始？", "whatAreYouThinking": "您在想什么，{name}？"}, "TemporaryChat": {"toggleTemporaryChat": "切换临时聊天", "temporaryChat": "临时聊天", "resetChat": "重置聊天", "thisChatWontBeSaved": "此聊天不会被保存。", "feelFreeToAskAnythingTemporarily": "请随时临时询问任何问题", "temporaryChatInstructions": "临时聊天说明", "temporaryChatInstructionsPlaceholder": "在此输入您的说明", "temporaryChatInstructionsDescription": "您可以为临时聊天设置说明。这将用作临时聊天的系统提示。"}, "placeholder": "您想了解什么？", "Tool": {"selectToolMode": "选择工具模式", "autoToolModeDescription": "自动决定何时使用工具，无需询问您", "manualToolModeDescription": "在使用任何工具前询问您的许可", "noneToolModeDescription": "不使用工具", "toolsSetup": "工具设置", "preset": "预设", "toolPresets": "工具预设", "saveAsPreset": "保存为预设", "saveAsPresetDescription": "将当前工具配置保存为预设。", "noPresetsAvailableYet": "暂无可用预设", "presetNameCannotBeEmpty": "预设名称不能为空", "presetNameAlreadyExists": "预设名称已存在", "presetSaved": "预设已保存", "chartTools": "图表工具", "clickSaveAsPresetToGetStarted": "点击保存为预设开始。"}, "VoiceChat": {"title": "语音聊天模式", "compactDisplayMode": "紧凑显示模式", "conversationDisplayMode": "对话显示模式", "pleaseCloseTheVoiceChatAndTryAgain": "请关闭语音聊天并重试。", "startConversation": "开始对话", "closeMic": "关闭麦克风", "openMic": "打开麦克风", "endConversation": "结束对话", "toggleVoiceChat": "切换语音聊天"}, "Thread": {"chat": "聊天", "summarizeAsProject": "总结为项目", "renameChat": "重命名", "deleteChat": "删除聊天", "failedToDeleteThread": "删除线程失败", "threadDeleted": "线程已删除", "failedToUpdateThread": "更新线程失败", "titleRequired": "标题是必需的", "threadUpdated": "线程已更新", "areYouSureYouWantToDeleteThisChatThread": "您确定要删除此聊天线程吗？"}, "Project": {"project": "项目", "renameProject": "重命名项目", "deleteProject": "删除项目", "placeholder": "例如：您是一个中国旅游指南聊天机器人。仅用中文回答，为每个行程项目包含精确的时间，并以表格形式简洁地呈现交通、预算和餐饮建议。", "generateWithAI": "用 AI 生成", "instructions": "说明", "projectName": "名称", "enterNameForNewProject": "为您的新项目输入名称", "provideCustomInstructionsForYourProjectAssistant": "为您的项目助手提供自定义说明", "createProject": "创建项目", "whatIsAProject": "什么是项目？", "aProjectAllowsYouToOrganizeYourFilesAndCustomInstructionsInOneConvenientPlace": "项目允许您在一个便利的地方组织您的文件和自定义说明。", "failedToUpdateProject": "更新项目失败", "projectUpdated": "项目已更新", "failedToDeleteProject": "删除项目失败", "projectDeleted": "项目已删除", "projectCreated": "项目已创建", "creatingChat": "正在创建聊天...", "chatInThisProjectCanAccessFileContents": "此项目中的聊天可以访问文件内容。", "writeHowTheChatbotShouldRespondToThisProjectOrWhatInformationItNeeds": "写下聊天机器人应该如何响应此项目或需要什么信息。", "conversationList": "对话列表", "enterNewPromptToStartYourFirstConversation": "输入新提示开始您的第一次对话", "noConversationsYet": "暂无对话", "projectInstructions": "项目说明", "projectInstructionsUpdated": "项目说明已更新", "howCanTheChatBotBestHelpYouWithThisProject": "聊天机器人如何最好地帮助您完成此项目？", "youCanAskTheChatBotToFocusOnASpecificTopicOrToRespondInAParticularToneOrFormat": "您可以要求聊天机器人专注于特定主题或以特定语调或格式回应。"}, "ChatPreferences": {"title": "聊天偏好", "whatShouldWeCallYou": "我们应该怎么称呼您？", "whatBestDescribesYourWork": "什么最能描述您的工作？", "whatPersonalPreferencesShouldBeTakenIntoAccountInResponses": "在回应中应该考虑哪些个人偏好？", "responseStyleExample1": "例如：保持解释简洁而切题", "responseStyleExample2": "例如：学习新概念时，我发现类比特别有用", "responseStyleExample3": "例如：在给出详细答案前先询问澄清问题", "responseStyleExample4": "例如：记住我主要用 Python 编程（不是编程初学者）", "professionExample1": "例如：软件工程师", "professionExample2": "例如：产品经理", "professionExample3": "例如：营销经理", "professionExample4": "例如：销售经理", "professionExample5": "例如：业务分析师", "preferencesSaved": "偏好已保存", "failedToSavePreferences": "保存偏好失败"}}, "Layout": {"toggleSidebar": "切换侧边栏", "newChat": "新聊天", "mcpConfiguration": "MCP 配置", "projects": "项目", "newProject": "新项目", "createProject": "创建项目", "toOrganizeIdeas": "整理您的想法", "today": "今天", "yesterday": "昨天", "lastWeek": "最近 7 天", "older": "更早", "recentChats": "最近聊天", "deleteAllChats": "删除所有聊天", "noConversationsYet": "暂无对话", "deletingAllChats": "正在删除所有线程...", "allChatsDeleted": "所有线程已删除", "failedToDeleteAllChats": "删除所有线程失败", "chatPreferences": "聊天偏好", "keyboardShortcuts": "键盘快捷键", "theme": "主题", "signOut": "登出", "language": "语言", "showMoreProjects": "显示更多 {count} 个", "showLessProjects": "显示更少", "showAllChats": "查看所有聊天", "showLessChats": "显示更少", "reportAnIssue": "报告问题", "joinCommunity": "加入社区"}, "KeyboardShortcuts": {"title": "键盘快捷键", "newChat": "新聊天", "toggleTemporaryChat": "切换临时聊天", "toggleSidebar": "切换侧边栏", "toolMode": "工具模式", "lastMessageCopy": "复制最后一条消息", "openChatPreferences": "打开聊天偏好", "deleteThread": "删除聊天", "openShortcutsPopup": "打开快捷键弹窗"}, "MCP": {"marketplace": "市场", "addMcpServer": "添加服务器", "configureYourMcpServerConnectionSettings": "配置您的 MCP 服务器连接设置", "mcpConfiguration": "MCP 配置", "nameMustContainOnlyAlphanumericCharactersAndHyphens": "名称只能包含字母数字字符 (A-Z, a-z, 0-9) 和连字符 (-)", "nameIsRequired": "名称是必需的", "configurationSavedSuccessfully": "配置保存成功", "enterMcpServerName": "输入 MCP 服务器名称", "saveConfiguration": "保存配置", "toolsTest": "工具测试", "refresh": "刷新", "delete": "删除", "edit": "编辑", "configuration": "配置", "availableTools": "可用工具", "noToolsAvailable": "无可用工具", "overviewTitle": "连接您的第一个服务器", "overviewDescription": "添加 MCP 服务器以解锁强大的 AI 集成", "searchTools": "搜索工具", "detail": "详情", "noSchemaPropertiesAvailable": "无可用架构属性", "createInputWithAI": "用 AI 创建输入", "generateExampleInputJSON": "生成示例输入 JSON", "enterPromptToGenerateExampleInputJSON": "输入提示为所选工具生成示例输入 JSON。", "callTool": "调用工具"}}