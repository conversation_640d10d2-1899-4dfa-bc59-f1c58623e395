{"Common": {"cancel": "Cancel", "update": "Update", "continue": "Continue", "delete": "Delete", "save": "Save", "back": "Back", "next": "Next", "create": "Create", "showLess": "Show less", "showMore": "Show more", "generate": "Generate"}, "Auth": {"SignIn": {"title": "Welcome Back", "description": "Sign in to continue to your account", "oauthClientIdNotSet": "{provider} client ID is not set", "noAccount": "Don't have an account? ", "signUp": "Sign up", "signIn": "Sign in", "orContinueWith": "OR CONTINUE WITH"}, "SignUp": {"title": "Create an account", "signIn": "Sign in", "description": "Sign up to your account", "step1": "Start your journey with us by entering your email address", "step2": "I'll use this name when we chat", "step3": "Create a strong password to secure your account", "signUp": "Sign Up", "invalidEmail": "Invalid email address", "emailAlreadyExists": "Email already exists", "nameRequired": "Name is required", "passwordRequired": "Password is required", "createAccount": "Create account"}, "Intro": {"description": "Welcome to MCP Chat Bot. Sign in to experience our AI-powered conversational tools."}}, "Chat": {"Error": "<PERSON><PERSON>", "thisMessageWasNotSavedPleaseTryTheChatAgain": "This message was not saved. Please try the chat again.", "Greeting": {"goodMorning": "Good morning, {name}", "goodAfternoon": "Good afternoon, {name}", "goodEvening": "Good evening, {name}", "niceToSeeYouAgain": "Nice to see you again, {name}", "whatAreYouWorkingOnToday": "What are you working on today? {name}", "letMeKnowWhenYoureReadyToBegin": "Let me know when you're ready to begin.", "whatAreYourThoughtsToday": "What are your thoughts today?", "whereWouldYouLikeToStart": "Where would you like to start?", "whatAreYouThinking": "What are you thinking? {name}"}, "TemporaryChat": {"toggleTemporaryChat": "Toggle Temporary Chat", "temporaryChat": "Temporary Chat", "resetChat": "<PERSON><PERSON>", "thisChatWontBeSaved": "This chat won't be saved.", "feelFreeToAskAnythingTemporarily": "Feel free to ask anything temporarily", "temporaryChatInstructions": "Temporary Chat Instructions", "temporaryChatInstructionsPlaceholder": "Enter your instructions here", "temporaryChatInstructionsDescription": "You can set instructions for the temporary chat. This will be used as a system prompt for the temporary chat."}, "placeholder": "What do you want to know?", "Tool": {"selectToolMode": "Select a tool mode", "autoToolModeDescription": "Decides when to use tools without asking you", "manualToolModeDescription": "Asks your permission before using any tools", "noneToolModeDescription": "Do not use tools", "toolsSetup": "Tools Setup", "preset": "Preset", "toolPresets": "Tool Presets", "saveAsPreset": "Save As Preset", "saveAsPresetDescription": "Save the current tool configuration as a preset.", "noPresetsAvailableYet": "No presets available yet", "presetNameCannotBeEmpty": "Preset name cannot be empty", "presetNameAlreadyExists": "Preset name already exists", "presetSaved": "Preset saved", "chartTools": "Chart Tools", "clickSaveAsPresetToGetStarted": "Click Save As Preset to get started."}, "VoiceChat": {"title": "Voice Chat Mode", "compactDisplayMode": "Compact display mode", "conversationDisplayMode": "Conversation display mode", "pleaseCloseTheVoiceChatAndTryAgain": "Please close the voice chat and try again.", "startConversation": "Start conversation", "closeMic": "Close Mic", "openMic": "Open Mic", "endConversation": "End conversation", "toggleVoiceChat": "Toggle Voice Chat", "readyWhenYouAreJustStartTalking": "Ready when you are—just start talking.", "yourMicIsOff": "Your mic is off.", "preparing": "Preparing...", "startVoiceChat": "Start voice chat?"}, "Thread": {"chat": "Cha<PERSON>", "summarizeAsProject": "Summarize as Project", "renameChat": "Re Name", "deleteChat": "Delete Chat", "failedToDeleteThread": "Failed to delete thread", "threadDeleted": "Thread deleted", "failedToUpdateThread": "Failed to update thread", "titleRequired": "Title is required", "threadUpdated": "Thread updated", "areYouSureYouWantToDeleteThisChatThread": "Are you sure you want to delete this Chat thread?"}, "Project": {"project": "Project", "renameProject": "Rename Project", "deleteProject": "Delete Project", "placeholder": "e.g. You are a Korean travel guide ChatBot. Respond only in Korean, include precise times for every itinerary item, and present transportation, budget, and dining recommendations succinctly in a table format.", "generateWithAI": "Generate With AI", "instructions": "Instructions", "projectName": "Name", "enterNameForNewProject": "Enter a name for your new project", "provideCustomInstructionsForYourProjectAssistant": "Provide custom instructions for your project assistant", "createProject": "Create Project", "whatIsAProject": "What is a project?", "aProjectAllowsYouToOrganizeYourFilesAndCustomInstructionsInOneConvenientPlace": "A project allows you to organize your files and custom instructions in one convenient place.", "failedToUpdateProject": "Failed to update project", "projectUpdated": "Project updated", "failedToDeleteProject": "Failed to delete project", "projectDeleted": "Project deleted", "projectCreated": "Project created", "creatingChat": "Creating Chat...", "chatInThisProjectCanAccessFileContents": "Chat in this project can access file contents.", "writeHowTheChatbotShouldRespondToThisProjectOrWhatInformationItNeeds": "Write how the chatbot should respond to this project or what information it needs.", "conversationList": "Conversation List", "enterNewPromptToStartYourFirstConversation": "Enter a new prompt to start your first conversation", "noConversationsYet": "No conversations yet", "projectInstructions": "Project Instructions", "projectInstructionsUpdated": "Project instructions updated", "howCanTheChatBotBestHelpYouWithThisProject": "How can the ChatBot best help you with this project?", "youCanAskTheChatBotToFocusOnASpecificTopicOrToRespondInAParticularToneOrFormat": "You can ask the ChatBot to focus on a specific topic or to respond in a particular tone or format."}, "ChatPreferences": {"title": "Chat Preferences", "whatShouldWeCallYou": "What should we call you?", "whatBestDescribesYourWork": "What best describes your work?", "whatPersonalPreferencesShouldBeTakenIntoAccountInResponses": "What personal preferences should be taken into account in responses?", "responseStyleExample1": "e.g. keep explanations brief and to the point", "responseStyleExample2": "e.g. when learning new concepts, I find analogies particularly helpful", "responseStyleExample3": "e.g. ask clarifying questions before giving detailed answers", "responseStyleExample4": "e.g. remember I primarily code in Python (not a coding beginner)", "professionExample1": "e.g. software engineer", "professionExample2": "e.g. product manager", "professionExample3": "e.g. marketing manager", "professionExample4": "e.g. sales manager", "professionExample5": "e.g. business analyst", "preferencesSaved": "Preferences saved", "failedToSavePreferences": "Failed to save preferences"}}, "Layout": {"toggleSidebar": "Toggle Sidebar", "newChat": "New Chat", "mcpConfiguration": "MCP Configuration", "projects": "Projects", "newProject": "New Project", "createProject": "Create a project", "toOrganizeIdeas": "To organize your ideas", "today": "Today", "yesterday": "Yesterday", "lastWeek": "Last 7 days", "older": "Older", "recentChats": "Recent Chats", "deleteAllChats": "Delete All Chats", "noConversationsYet": "No conversations yet", "deletingAllChats": "Deleting all threads...", "allChatsDeleted": "All threads deleted", "failedToDeleteAllChats": "Failed to delete all threads", "chatPreferences": "Chat Preferences", "keyboardShortcuts": "Keyboard Shortcuts", "theme": "Theme", "signOut": "Sign out", "language": "Language", "showMoreProjects": "Show {count} more", "showLessProjects": "Show less", "showAllChats": "View All Chats", "showLessChats": "Show less", "reportAnIssue": "Report an issue", "joinCommunity": "Join Community"}, "KeyboardShortcuts": {"title": "Keyboard Shortcuts", "newChat": "New Chat", "toggleTemporaryChat": "Toggle Temporary Chat", "toggleSidebar": "Toggle Sidebar", "toolMode": "Tool Mode", "lastMessageCopy": "Copy Last Message", "openChatPreferences": "Open Chat Preferences", "deleteThread": "Delete Chat", "openShortcutsPopup": "Open Shortcuts Popup"}, "MCP": {"marketplace": "Marketplace", "addMcpServer": "Add Server", "configureYourMcpServerConnectionSettings": "Configure your MCP server connection settings", "mcpConfiguration": "MCP Configuration", "nameMustContainOnlyAlphanumericCharactersAndHyphens": "Name must contain only alphanumeric characters (A-Z, a-z, 0-9) and hyphens (-)", "nameIsRequired": "Name is required", "configurationSavedSuccessfully": "Configuration saved successfully", "enterMcpServerName": "Enter MCP server name", "saveConfiguration": "Save Configuration", "toolsTest": "Tools Test", "refresh": "Refresh", "delete": "Delete", "edit": "Edit", "configuration": "Configuration", "availableTools": "Available Tools", "noToolsAvailable": "No tools available", "overviewTitle": "Connect Your First Server", "overviewDescription": "Add MCP servers to unlock powerful AI integrations", "searchTools": "Search tools", "detail": "Detail", "noSchemaPropertiesAvailable": "No schema properties available", "createInputWithAI": "Create Input with AI", "generateExampleInputJSON": "Generate Example Input JSON", "enterPromptToGenerateExampleInputJSON": "Enter a prompt to generate example input JSON for the selected tool.", "callTool": "Call Tool"}}