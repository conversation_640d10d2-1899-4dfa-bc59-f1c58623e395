name: PR Title Check
permissions:
  contents: read
  pull-requests: write
  issues: write
  statuses: write
  checks: write
on:
  pull_request:
    types: [opened, synchronize, reopened, edited]

jobs:
  danger:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Run Danger
        run: npx danger ci --dangerfile=./.github/.dangerfile.ts
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
