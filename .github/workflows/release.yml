on:
  push:
    branches:
      - main
permissions:
  contents: write
  pull-requests: write

name: release-please

jobs:
  release-please:
    runs-on: ubuntu-latest
    steps:
      - uses: googleapis/release-please-action@v4
        with:
          # this assumes that you have created a personal access token
          # (PAT) and configured it as a GitHub action secret named
          # `MY_RELEASE_PLEASE_TOKEN` (this secret name is not important).
          token: ${{ secrets.GITHUB_TOKEN }}
          # this is a built-in strategy in release-please, see "Action Inputs"
          # for more options
          release-type: node
